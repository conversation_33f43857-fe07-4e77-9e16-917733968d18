import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@netlify/functions";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp, getApps, cert } from "firebase-admin/app";

// Initialize Firebase Admin if not already initialized
if (getApps().length === 0) {
  initializeApp({
    credential: cert(JSON.parse(process.env.FIREBASE_ADMIN_KEY || '{}')),
  });
}

const auth = getAuth();
const db = getFirestore();

const handler: Handler = async (event) => {
  // Define allowed origin
  const allowedOrigin = "https://praxjobs.com"; // Your production frontend domain

  // Set default CORS headers
  const headers = {
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Content-Type": "application/json",
  };

  // Handle CORS preflight requests
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204,
      headers,
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: "Method not allowed. Use POST." }),
    };
  }

  try {
    // Get the authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ 
          error: 'Unauthorized - No valid token provided' 
        })
      };
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ 
          error: 'Unauthorized - Invalid token' 
        })
      };
    }

    const userId = decodedToken.uid;
    const { resumeId } = JSON.parse(event.body || '{}');

    if (!resumeId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          error: 'Missing resumeId parameter' 
        })
      };
    }

    // Get the resume from Firestore
    const resumeRef = db.collection('users').doc(userId).collection('resumes').doc(resumeId);
    const resumeDoc = await resumeRef.get();

    if (!resumeDoc.exists) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ 
          error: 'Resume not found' 
        })
      };
    }

    const resumeData = resumeDoc.data();
    
    if (!resumeData) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ 
          error: 'Resume data is empty' 
        })
      };
    }

    // Parse the resume content and extract structured data
    const parsedResume = await parseResumeContent(resumeData.content || '');

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: {
          ...parsedResume,
          resumeId: resumeId,
          resumeName: resumeData.name || 'Imported Resume'
        }
      })
    };

  } catch (error) {
    console.error('Error importing resume:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    };
  }
};

/**
 * Parse resume content and extract structured data for the resume builder
 */
async function parseResumeContent(content: string) {
  console.log('parseResumeContent - Using AI to parse resume content');
  
  try {
    const systemPrompt = `You are a resume parser. Extract structured data from the resume text and return it in the exact JSON format specified below.

IMPORTANT: Return ONLY valid JSON, no other text or explanation.

Expected JSON structure:
{
  "contactInfo": {
    "name": "Full name",
    "email": "<EMAIL>", 
    "phone": "phone number",
    "location": "city, state/country",
    "linkedin": "linkedin profile URL",
    "portfolio": "portfolio/website URL"
  },
  "summary": "Professional summary or objective (2-3 sentences)",
  "workExperience": [
    {
      "title": "Job title",
      "company": "Company name", 
      "location": "Location",
      "startDate": "Start date (e.g., Jan 2023)",
      "endDate": "End date (e.g., Present or Dec 2023)",
      "description": ["bullet point 1", "bullet point 2", "bullet point 3"]
    }
  ],
  "education": [
    {
      "degree": "Degree name (e.g., Bachelor of Science)",
      "school": "School/University name",
      "location": "Location",
      "startDate": "Start date",
      "endDate": "Graduation date",
      "description": ["additional details"]
    }
  ],
  "skills": ["skill1", "skill2", "skill3"]
}

Guidelines:
- Extract contact info from the header/top of resume
- For work experience, identify job titles, companies, dates, and bullet points
- For education, identify degrees, schools, and graduation dates  
- For skills, extract individual skills (technical, soft skills, etc.)
- If a field is not found, use empty string or empty array
- Dates should be in readable format (e.g., "Jan 2023", "Present")
- Keep bullet points concise but descriptive`;

    const response = await groq.chat.completions.create({
      model: "gemma2-9b-it",
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user", 
          content: `Parse this resume and return the structured data in JSON format:\n\n${content}`,
        },
      ],
      max_tokens: 4096,
      temperature: 0.1,
    });

    const aiResponse = response.choices?.[0]?.message?.content;
    
    if (!aiResponse) {
      console.error('parseResumeContent - No response from AI');
      return {
        contactInfo: { name: '', email: '', phone: '', location: '', linkedin: '', portfolio: '' },
        summary: '',
        workExperience: [],
        education: [],
        skills: []
      };
    }

    console.log('parseResumeContent - AI response:', aiResponse);

    // Try to extract JSON from the response
    let jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error('parseResumeContent - No JSON found in AI response');
      return {
        contactInfo: { name: '', email: '', phone: '', location: '', linkedin: '', portfolio: '' },
        summary: '',
        workExperience: [],
        education: [],
        skills: []
      };
    }

    const parsedData = JSON.parse(jsonMatch[0]);
    console.log('parseResumeContent - Parsed data:', JSON.stringify(parsedData, null, 2));

    return parsedData;

  } catch (error) {
    console.error('parseResumeContent - Error parsing with AI:', error);
    return {
      contactInfo: { name: '', email: '', phone: '', location: '', linkedin: '', portfolio: '' },
      summary: '',
      workExperience: [],
      education: [],
      skills: []
    };
  }
}

export { handler };
