---
import ContactInfo from "./sections/ContactInfo.astro";
import Summary from "./sections/Summary.astro";
import WorkExperience from "./sections/WorkExperience.astro";
import Education from "./sections/Education.astro";
import Skills from "./sections/Skills.astro";
import CustomSection from "./sections/CustomSection.astro";
---

<div class="space-y-4">
  <!-- Sticky Stepper -->
  <div id="form-stepper" class="sticky top-0 z-10 -mt-4 mb-2 py-2 bg-white/70 dark:bg-gray-900/70 backdrop-blur supports-[backdrop-filter]:backdrop-blur rounded-xl">
    <div class="flex items-center gap-2 overflow-auto no-scrollbar px-2">
      <button id="step-contact" data-target="contact" class="step-chip is-active" aria-current="step">
        <span class="icon-[lucide--user] mr-2 h-4 w-4"></span>Contact
      </button>
      <button id="step-summary" data-target="summary" class="step-chip">
        <span class="icon-[lucide--file-text] mr-2 h-4 w-4"></span>Summary
      </button>
      <button id="step-work" data-target="work" class="step-chip">
        <span class="icon-[lucide--briefcase] mr-2 h-4 w-4"></span>Experience
      </button>
      <button id="step-education" data-target="education" class="step-chip">
        <span class="icon-[lucide--graduation-cap] mr-2 h-4 w-4"></span>Education
      </button>
      <button id="step-skills" data-target="skills" class="step-chip">
        <span class="icon-[lucide--sparkles] mr-2 h-4 w-4"></span>Skills
      </button>
      <button id="step-custom" data-target="custom" class="step-chip">
        <span class="icon-[lucide--plus-circle] mr-2 h-4 w-4"></span>Custom
      </button>
    </div>
  </div>
  <!-- Accordion Item 1: Contact Info -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-contact" class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="true" aria-controls="collapse-contact">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
          Contact Information
        </div>
        <div class="flex items-center ml-auto">
          <span id="contact-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div id="collapse-contact" class="accordion-collapse show" role="region" aria-labelledby="btn-contact">
      <div class="accordion-body py-4 px-5">
        <ContactInfo />
      </div>
    </div>
  </div>

  <!-- Accordion Item 2: Summary -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-summary" class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="false" aria-controls="collapse-summary">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
          Summary / Objective
        </div>
        <div class="flex items-center ml-auto">
          <span id="summary-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div id="collapse-summary" class="accordion-collapse" role="region" aria-labelledby="btn-summary">
      <div class="accordion-body py-4 px-5">
        <Summary />
      </div>
    </div>
  </div>

  <!-- Accordion Item 3: Work Experience -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-work" class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="false" aria-controls="collapse-work">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
          Work Experience
        </div>
        <div class="flex items-center ml-auto">
          <span id="work-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div id="collapse-work" class="accordion-collapse" role="region" aria-labelledby="btn-work">
      <div class="accordion-body py-4 px-5">
        <WorkExperience />
      </div>
    </div>
  </div>

  <!-- Accordion Item 4: Education -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-education" class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="false" aria-controls="collapse-education">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">4</span>
          Education
        </div>
        <div class="flex items-center ml-auto">
          <span id="education-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div id="collapse-education" class="accordion-collapse" role="region" aria-labelledby="btn-education">
      <div class="accordion-body py-4 px-5">
        <Education />
      </div>
    </div>
  </div>

  <!-- Accordion Item 5: Skills -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-skills" class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="false" aria-controls="collapse-skills">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">5</span>
          Skills
        </div>
        <div class="flex items-center ml-auto">
          <span id="skills-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div id="collapse-skills" class="accordion-collapse" role="region" aria-labelledby="btn-skills">
      <div class="accordion-body py-4 px-5">
        <Skills />
      </div>
    </div>
  </div>

  <!-- Accordion Item 6: Custom Sections -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button id="btn-custom" class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transition-colors" type="button" aria-expanded="false" aria-controls="collapse-custom">
        Add Custom Section
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div id="collapse-custom" class="accordion-collapse" role="region" aria-labelledby="btn-custom">
      <div class="accordion-body py-4 px-5">
        <CustomSection />
      </div>
    </div>
  </div>
</div>

<script>
  import { resumeData } from '../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const accordionButtons = document.querySelectorAll('.accordion-button');
    const stepper = document.getElementById('form-stepper');

    const slideDown = (element: HTMLElement) => {
      element.style.display = 'block';
      const height = element.scrollHeight;
      element.style.height = '0px';
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '0';
      setTimeout(() => {
        element.style.height = `${height}px`;
        element.style.opacity = '1';
      }, 0);
      setTimeout(() => {
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    const slideUp = (element: HTMLElement) => {
      const height = element.scrollHeight;
      element.style.height = `${height}px`;
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '1';
      setTimeout(() => {
        element.style.height = '0px';
        element.style.opacity = '0';
      }, 0);
      setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    // Section completion tracking
    const updateSectionStatus = (sectionName: string, isComplete: boolean) => {
      const statusElement = document.getElementById(`${sectionName}-status`);
      if (statusElement) {
        statusElement.className = isComplete
          ? 'w-2 h-2 bg-green-500 rounded-full mr-2'
          : 'w-2 h-2 bg-gray-300 rounded-full mr-2';
      }

      // Reflect status on step chips
      const step = document.getElementById(`step-${sectionName}`);
      if (step) {
        step.classList.toggle('is-complete', isComplete);
      }
    };

    // Check section completion
    const checkSectionCompletion = (data: any) => {
      if (data.contactInfo) {
        const hasRequiredContact = data.contactInfo.fullName && data.contactInfo.email;
        updateSectionStatus('contact', hasRequiredContact);
      }

      if (data.summary) {
        updateSectionStatus('summary', data.summary.trim().length > 0);
      }

      if (data.workExperience) {
        updateSectionStatus('work', data.workExperience.length > 0);
      }

      if (data.education) {
        updateSectionStatus('education', data.education.length > 0);
      }

      if (data.skills) {
        updateSectionStatus('skills', data.skills.length > 0);
      }
    };

    // Subscribe to data changes
    const unsubscribe = resumeData.subscribe(checkSectionCompletion);

    // Initial check
    checkSectionCompletion(resumeData.get());

    // Cleanup subscription on component unload
    window.addEventListener('beforeunload', unsubscribe);

    accordionButtons.forEach(button => {
      const parent = button.parentElement;
      if (!parent) return;

      const collapse = parent.nextElementSibling as HTMLElement;
      if (!collapse) return;

      const icon = button.querySelector('span');

      // By default, all sections except the custom one are open.
      if (!button.classList.contains('collapsed')) {
        collapse.style.display = 'block';
        if (icon) icon.style.transform = 'rotate(0deg)';
        button.setAttribute('aria-expanded', 'true');
      } else {
        if (icon) icon.style.transform = 'rotate(-90deg)';
        button.setAttribute('aria-expanded', 'false');
      }

      button.addEventListener('click', () => {
        // Close all other accordion items
        accordionButtons.forEach(otherButton => {
          if (otherButton !== button) {
            const parent = otherButton.parentElement;
            if (parent) {
              const otherCollapse = parent.nextElementSibling as HTMLElement;
              const otherIcon = otherButton.querySelector('span');
              if (otherCollapse && !otherButton.classList.contains('collapsed')) {
                otherButton.classList.add('collapsed');
                slideUp(otherCollapse);
                if (otherIcon) otherIcon.style.transform = 'rotate(-90deg)';
                otherButton.setAttribute('aria-expanded', 'false');
              }
            }
          }
        });

        // Toggle the clicked accordion item
        const isCollapsed = button.classList.contains('collapsed');
        if (isCollapsed) {
          button.classList.remove('collapsed');
          slideDown(collapse);
          if (icon) icon.style.transform = 'rotate(0deg)';
          button.setAttribute('aria-expanded', 'true');
        } else {
          button.classList.add('collapsed');
          slideUp(collapse);
          if (icon) icon.style.transform = 'rotate(-90deg)';
          button.setAttribute('aria-expanded', 'false');
        }
      });
    });

    // Stepper interactions
    if (stepper) {
      stepper.addEventListener('click', (e) => {
        const btn = (e.target as HTMLElement).closest<HTMLButtonElement>('[data-target]');
        if (!btn) return;
        const target = btn.dataset.target;
        const map: Record<string, string> = {
          contact: 'btn-contact',
          summary: 'btn-summary',
          work: 'btn-work',
          education: 'btn-education',
          skills: 'btn-skills',
          custom: 'btn-custom',
        };
        const accBtn = document.getElementById(map[target || '']) as HTMLButtonElement | null;
        if (accBtn) {
          // Mark active chip
          stepper.querySelectorAll('.step-chip').forEach(el => el.classList.remove('is-active'));
          btn.classList.add('is-active');
          // Open the section
          if (accBtn.classList.contains('collapsed')) accBtn.click();
          accBtn.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    }
  });
</script>

<style>
  /* Stepper chips */
  .step-chip {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    background: rgba(99,102,241,0.06);
    color: rgb(55,65,81);
    border: 1px solid rgba(99,102,241,0.18);
    font-size: 0.75rem;
    font-weight: 600;
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }
  .dark .step-chip { color: rgb(229,231,235); background: rgba(99,102,241,0.09); border-color: rgba(99,102,241,0.25); }
  .step-chip:hover { background: rgba(99,102,241,0.12); }
  .step-chip.is-active { background: rgba(99,102,241,0.18); border-color: rgba(99,102,241,0.4); }
  .step-chip.is-complete { border-color: rgb(34,197,94); box-shadow: inset 0 0 0 1px rgba(34,197,94,0.3); }

  .accordion-button.collapsed .icon-[lucide--chevron-down] {
    transform: rotate(-90deg);
  }
  .accordion-collapse {
    display: none;
  }
</style>