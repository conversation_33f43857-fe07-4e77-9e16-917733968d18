---
import ContactInfo from "./sections/ContactInfo.astro";
import Summary from "./sections/Summary.astro";
import WorkExperience from "./sections/WorkExperience.astro";
import Education from "./sections/Education.astro";
import Skills from "./sections/Skills.astro";
import CustomSection from "./sections/CustomSection.astro";
---

<section id="editor-canvas" class="space-y-6">
  <div id="editor-contact" class="editor-section"><ContactInfo /></div>
  <div id="editor-summary" class="editor-section hidden"><Summary /></div>
  <div id="editor-work" class="editor-section hidden"><WorkExperience /></div>
  <div id="editor-education" class="editor-section hidden"><Education /></div>
  <div id="editor-skills" class="editor-section hidden"><Skills /></div>
  <div id="editor-custom" class="editor-section hidden"><CustomSection /></div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const sections = ['contact','summary','work','education','skills','custom'] as const;
    const show = (id: (typeof sections)[number]) => {
      sections.forEach(s => {
        const el = document.getElementById('editor-' + s);
        if (!el) return;
        el.classList.toggle('hidden', s !== id);
      });
      window.dispatchEvent(new CustomEvent('section:changed', { detail: { section: id } }));
    };
    // Listen to nav selections
    window.addEventListener('section:select', (e: any) => show(e.detail.section));
    // Initial section from preference
    const initial = (localStorage.getItem('prax_resume_active_section') as any) || 'contact';
    show(initial);
  });
</script>

<style>
  .editor-section > * {
    background: rgba(255,255,255,0.8);
    border: 1px solid rgba(229,231,235,0.6);
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.06);
    padding: 1rem;
  }
  .dark .editor-section > * {
    background: rgba(31,41,55,0.6);
    border-color: rgba(55,65,81,0.6);
  }
</style>


