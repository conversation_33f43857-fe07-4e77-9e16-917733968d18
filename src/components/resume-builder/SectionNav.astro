---
import type { } from 'astro';
---

<nav>
  <!-- Mobile chips bar -->
  <div class="lg:hidden sticky top-0 z-10 -mt-2 mb-3 px-1 py-2 bg-white/70 dark:bg-gray-900/70 backdrop-blur rounded-xl">
    <div id="nav-chips" class="flex items-center gap-2 overflow-x-auto no-scrollbar">
      <button class="chip" data-section="contact">Contact</button>
      <button class="chip" data-section="summary">Summary</button>
      <button class="chip" data-section="work">Experience</button>
      <button class="chip" data-section="education">Education</button>
      <button class="chip" data-section="skills">Skills</button>
      <button class="chip" data-section="custom">Custom</button>
    </div>
  </div>

  <!-- Desktop vertical rail -->
  <aside class="hidden lg:block">
    <ul id="nav-rail" class="space-y-1">
      <li><button class="rail-item" data-section="contact"><span class="dot" id="dot-contact"></span>Contact</button></li>
      <li><button class="rail-item" data-section="summary"><span class="dot" id="dot-summary"></span>Summary</button></li>
      <li><button class="rail-item" data-section="work"><span class="dot" id="dot-work"></span>Experience</button></li>
      <li><button class="rail-item" data-section="education"><span class="dot" id="dot-education"></span>Education</button></li>
      <li><button class="rail-item" data-section="skills"><span class="dot" id="dot-skills"></span>Skills</button></li>
      <li><button class="rail-item" data-section="custom"><span class="dot" id="dot-custom"></span>Custom</button></li>
    </ul>
  </aside>
</nav>

<script>
  import { resumeData } from '../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const PREF_KEY = 'prax_resume_active_section';
    const setActiveUi = (section) => {
      document.querySelectorAll('.chip, .rail-item').forEach(el => el.classList.toggle('active', (el as HTMLElement).dataset.section === section));
    };

    const emit = (section) => {
      window.dispatchEvent(new CustomEvent('section:select', { detail: { section } }));
      setActiveUi(section);
      localStorage.setItem(PREF_KEY, section);
    };

    const initial = localStorage.getItem(PREF_KEY) || 'contact';
    setActiveUi(initial);
    // Sync active when other components change it
    window.addEventListener('section:changed', (e: any) => setActiveUi(e.detail.section));

    document.querySelectorAll('.chip, .rail-item').forEach(btn => {
      btn.addEventListener('click', () => emit((btn as HTMLElement).dataset.section));
    });

    // Completion dots
    const updateDots = (data) => {
      const set = (id, ok) => {
        const el = document.getElementById('dot-' + id);
        if (el) el.className = 'dot ' + (ok ? 'ok' : '');
      };
      set('contact', !!(data.contactInfo?.fullName && data.contactInfo?.email));
      set('summary', !!(data.summary && data.summary.trim().length > 0));
      set('work', Array.isArray(data.workExperience) && data.workExperience.length > 0);
      set('education', Array.isArray(data.education) && data.education.length > 0);
      set('skills', Array.isArray(data.skills) && data.skills.length > 0);
      set('custom', Array.isArray(data.customSections) && data.customSections.some(s => s.title || s.content));
    };
    updateDots(resumeData.get());
    const unsub = resumeData.subscribe(updateDots);
    window.addEventListener('beforeunload', unsub);
  });
</script>

<style>
  .chip {
    padding: 0.5rem 0.75rem; border-radius: 9999px; font-weight: 600; font-size: 0.75rem;
    background: rgba(99,102,241,0.08); color: rgb(55,65,81); border: 1px solid rgba(99,102,241,0.18);
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }
  .chip.active { background: rgba(99,102,241,0.18); border-color: rgba(99,102,241,0.4); }
  .dark .chip { color: rgb(229,231,235); background: rgba(99,102,241,0.12); border-color: rgba(99,102,241,0.25); }

  .rail-item { width: 100%; text-align: left; padding: 0.5rem 0.75rem; border-radius: 0.5rem; font-weight: 600; font-size: 0.875rem;
    display: flex; align-items: center; gap: 0.5rem; color: rgb(55,65,81); transition: background-color 0.2s ease;
  }
  .rail-item:hover { background: rgba(99,102,241,0.08); }
  .rail-item.active { background: rgba(99,102,241,0.12); }
  .dark .rail-item { color: rgb(229,231,235); }
  .dot { height: 8px; width: 8px; border-radius: 9999px; background: #d1d5db; display: inline-block; }
  .dot.ok { background: #22c55e; }
</style>


