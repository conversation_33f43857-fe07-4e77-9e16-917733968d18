---
// Form section for the professional summary.
---

<div class="space-y-4">
  <div>
    <label for="summary" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Summary/Objective</label>
    <textarea id="summary" name="summary" rows="5" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"></textarea>
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      A brief 2-3 sentence summary of your professional experience and goals.
    </p>
  </div>
</div>

<script>
  import { resumeData, updateSection } from '../../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const summaryTextarea = document.getElementById('summary') as HTMLTextAreaElement;

    if (!summaryTextarea) return;

    // Initial population from store
    const currentData = resumeData.get();
    if (currentData.summary) {
      summaryTextarea.value = currentData.summary;
    }

    const unsubscribe = resumeData.subscribe(data => {
      console.log('Summary received data update:', data);
      if (summaryTextarea.value !== data.summary) {
        console.log(`Updating summary from "${summaryTextarea.value}" to "${data.summary}"`);
        summaryTextarea.value = data.summary;
      }
    });

    const debounce = (fn: (...args: any[]) => void, ms = 200) => {
      let t: number | null = null;
      return (...args: any[]) => {
        if (t) window.clearTimeout(t);
        t = window.setTimeout(() => fn(...args), ms);
      };
    };

    const debounced = debounce((value: string) => updateSection('summary', value));

    summaryTextarea.addEventListener('input', (e) => {
      const target = e.target as HTMLTextAreaElement;
      debounced(target.value);
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>