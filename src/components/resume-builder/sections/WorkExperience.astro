---
// Form section for work experience.
---

<div id="work-experience-container" class="space-y-6">
  <!-- Dynamic content will be injected here -->
</div>

<button type="button" id="add-experience-btn" class="w-full text-center mt-4 px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:border-primary hover:text-primary dark:hover:border-primary dark:hover:text-primary transition">
  + Add Another Experience
</button>

<template id="experience-template">
  <div class="p-4 border border-gray-200 dark:border-gray-700/50 rounded-lg space-y-4 experience-item">
    <div class="relative grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Job Title</label>
        <input type="text" name="jobTitle" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
        <input type="text" name="company" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <button type="button" class="remove-experience-btn absolute -top-2 -right-2 text-gray-400 hover:text-red-500 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </button>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
        <input type="text" name="location" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
        <input type="month" name="startDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
        <input type="month" name="endDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
    </div>
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Bullet Points</label>
      <div class="space-y-2 bullet-points-container">
        <!-- Bullet points will be dynamically added here -->
      </div>
      <button type="button" class="add-bullet-point-btn mt-2 text-sm text-primary hover:underline">
        + Add Bullet Point
      </button>
    </div>
  </div>
</template>

<template id="bullet-point-template">
  <div class="flex items-center gap-2 bullet-point-item">
    <input type="text" name="bulletPoint" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
    <button type="button" class="remove-bullet-point-btn text-gray-400 hover:text-red-500 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
    </button>
  </div>
</template>

<script>
  import { resumeData, addItem, removeItem, updateSection, type ResumeData } from '../../../lib/resumeBuilderService';

  type WorkExperience = ResumeData['workExperience'][number];
  type BulletPoint = WorkExperience['bulletPoints'][number];

  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('work-experience-container') as HTMLElement;
    const experienceTemplate = document.getElementById('experience-template') as HTMLTemplateElement;
    const bulletPointTemplate = document.getElementById('bullet-point-template') as HTMLTemplateElement;
    const addButton = document.getElementById('add-experience-btn') as HTMLButtonElement;

    if (!container || !experienceTemplate || !bulletPointTemplate || !addButton) return;

    const render = (workExperience: WorkExperience[]) => {
      const renderedItems = new Map(
        Array.from(container.querySelectorAll('.experience-item')).map(el => [(el as HTMLElement).dataset.id, el as HTMLElement])
      );

      renderedItems.forEach((el, id) => {
        if (!workExperience.some(item => item.id === id)) {
          el.remove();
        }
      });

      workExperience.forEach((item) => {
        let itemElement = renderedItems.get(item.id);

        if (!itemElement) {
          const clone = experienceTemplate.content.cloneNode(true) as DocumentFragment;
          const newItem = clone.querySelector('.experience-item') as HTMLElement;
          if (!newItem) return;
          itemElement = newItem;

          itemElement.dataset.id = item.id;
          container.appendChild(itemElement);

          const inputs = itemElement.querySelectorAll('input[name]') as NodeListOf<HTMLInputElement>;
          inputs.forEach(input => {
            const name = input.getAttribute('name') as keyof WorkExperience;
            if (name !== 'bulletPoints') {
              const debounce = (fn: (...args: any[]) => void, ms = 200) => {
                let t: number | null = null;
                return (...args: any[]) => {
                  if (t) window.clearTimeout(t);
                  t = window.setTimeout(() => fn(...args), ms);
                };
              };
              const debounced = debounce((value: string) => {
                const currentExperience = [...resumeData.get().workExperience];
                const experienceItem = currentExperience.find(exp => exp.id === item.id);
                if (experienceItem) {
                  (experienceItem as any)[name] = value;
                  updateSection('workExperience', currentExperience);
                }
              });
              input.addEventListener('input', (e) => debounced((e.target as HTMLInputElement).value));
            }
          });

          const removeButton = itemElement.querySelector('.remove-experience-btn') as HTMLButtonElement;
          if (removeButton) {
            removeButton.addEventListener('click', () => removeItem('workExperience', item.id));
          }

          const addBulletPointBtn = itemElement.querySelector('.add-bullet-point-btn') as HTMLButtonElement;
          if (addBulletPointBtn) {
            addBulletPointBtn.addEventListener('click', () => {
              const currentExperience = [...resumeData.get().workExperience];
              const experienceItem = currentExperience.find(exp => exp.id === item.id);
              if (experienceItem) {
                if (!experienceItem.bulletPoints) {
                  experienceItem.bulletPoints = [];
                }
                experienceItem.bulletPoints.push({ id: crypto.randomUUID(), text: '' });
                updateSection('workExperience', currentExperience);
              }
            });
          }
        }

        const inputs = itemElement.querySelectorAll('input[name]') as NodeListOf<HTMLInputElement>;
        inputs.forEach(input => {
          const name = input.getAttribute('name') as keyof WorkExperience;
          if (name in item && name !== 'bulletPoints') {
            if (input.value !== (item as any)[name]) {
              input.value = (item as any)[name];
            }
          }
        });

        const bulletPointsContainer = itemElement.querySelector('.bullet-points-container') as HTMLElement;
        if (bulletPointsContainer) {
          renderBulletPoints(bulletPointsContainer, item.id, item.bulletPoints || []);
        }
      });
    };

    const renderBulletPoints = (container: HTMLElement, experienceId: string, bulletPoints: BulletPoint[]) => {
      const renderedBulletPoints = new Map(
        Array.from(container.querySelectorAll('.bullet-point-item')).map(el => [(el as HTMLElement).dataset.id, el as HTMLElement])
      );

      renderedBulletPoints.forEach((el, id) => {
        if (!bulletPoints.some(bp => bp.id === id)) {
          el.remove();
        }
      });

      bulletPoints.forEach((bp) => {
        let bpElement = renderedBulletPoints.get(bp.id);

        if (!bpElement) {
          const clone = bulletPointTemplate.content.cloneNode(true) as DocumentFragment;
          const newItem = clone.querySelector('.bullet-point-item') as HTMLElement;
          if (!newItem) return;
          bpElement = newItem;

          bpElement.dataset.id = bp.id;
          container.appendChild(bpElement);

          const input = bpElement.querySelector('input[name="bulletPoint"]') as HTMLInputElement;
           if (input) {
             const debounce = (fn: (...args: any[]) => void, ms = 200) => {
               let t: number | null = null;
               return (...args: any[]) => {
                 if (t) window.clearTimeout(t);
                 t = window.setTimeout(() => fn(...args), ms);
               };
             };
             const debounced = debounce((value: string) => {
               const currentExperience = [...resumeData.get().workExperience];
               const experienceItem = currentExperience.find(exp => exp.id === experienceId);
               if (experienceItem && experienceItem.bulletPoints) {
                 const bulletPoint = experienceItem.bulletPoints.find(p => p.id === bp.id);
                 if (bulletPoint) {
                   bulletPoint.text = value;
                   updateSection('workExperience', currentExperience);
                 }
               }
             });
             input.addEventListener('input', (e) => debounced((e.target as HTMLInputElement).value));
           }

          const removeButton = bpElement.querySelector('.remove-bullet-point-btn') as HTMLButtonElement;
          if (removeButton) {
            removeButton.addEventListener('click', () => {
              const currentExperience = [...resumeData.get().workExperience];
              const experienceItem = currentExperience.find(exp => exp.id === experienceId);
              if (experienceItem && experienceItem.bulletPoints) {
                experienceItem.bulletPoints = experienceItem.bulletPoints.filter(p => p.id !== bp.id);
                updateSection('workExperience', currentExperience);
              }
            });
          }
        }

        const input = bpElement.querySelector('input[name="bulletPoint"]') as HTMLInputElement;
        if (input && input.value !== bp.text) {
          input.value = bp.text;
        }
      });
    };

    const currentData = resumeData.get();
    if (currentData.workExperience) {
      render(currentData.workExperience);
    }

    const unsubscribe = resumeData.subscribe(data => {
      if (data.workExperience) {
        render(data.workExperience);
      }
    });

    addButton.addEventListener('click', () => {
      addItem('workExperience', { bulletPoints: [] });
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>