import type { APIRoute } from 'astro';
import * as textract from 'textract';
import Groq from 'groq-sdk';

const groq = new Groq({
    apiKey: import.meta.env.GROQ_API_KEY ?? ''
});

const SUPPORTED_MIME_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/rtf',
    'application/rtf'
];

// Async text extraction function
async function extractTextFromBuffer(fileBuffer: Buffer, fileType: string): Promise<string> {
    return new Promise((resolve, reject) => {
        textract.fromBufferWithMime(fileType, fileBuffer, (error, text) => {
            if (error) {
                reject(new Error(`Text extraction failed: ${error.message}`));
            } else {
                resolve(text || '');
            }
        });
    });
}

async function cleanResumeText(rawText: string): Promise<string> {
    if (!rawText) {
        return rawText;
    }

    try {
        if (rawText.trim().length === 0) {
            return rawText;
        }

        if (!groq) {
            return rawText;
        }

        let response;
        try {
            response = await groq.chat.completions.create({
                model: 'llama-3.1-8b-instant',
                messages: [
                    {
                        role: "system", 
                        content: `You are a professional resume text cleaner. 
- Preserve ALL original content
- Remove extra whitespaces
- Ensure consistent formatting
- Correct minor typographical errors
- Maintain original document structure
- Return ONLY the cleaned text
- Do NOT add or remove any information`
                    },
                    {
                        role: "user", 
                        content: rawText
                    }
                ],
                max_tokens: 4096,
                temperature: 0.1
            });
        } catch (aiCallError) {
            return rawText;
        }

        const content = response.choices?.[0]?.message?.content;
        return content && typeof content === 'string' 
            ? content.trim() 
            : rawText;

    } catch (error) {
        return rawText;
    }
}

/**
 * Parse resume content and extract structured data for the resume builder
 */
interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  location: string;
  linkedin: string;
  portfolio: string;
}

interface WorkExperience {
  title: string;
  company: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface Education {
  degree: string;
  school: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface ParsedResumeData {
  contactInfo: ContactInfo;
  summary: string;
  workExperience: WorkExperience[];
  education: Education[];
  skills: string[];
}

/**
 * Parse resume content and extract structured data for the resume builder
 */
async function parseResumeContent(content: string): Promise<ParsedResumeData> {
  // Basic parsing logic - you can enhance this with more sophisticated parsing
  const lines = content.split('\n').map(line => line.trim()).filter(line => line);
  
  const parsedData: ParsedResumeData = {
    contactInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
      linkedin: '',
      portfolio: ''
    },
    summary: '',
    workExperience: [],
    education: [],
    skills: []
  };

  let currentSection = '';
  let currentExperience: WorkExperience | null = null;
  let currentEducation: Education | null = null;

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Detect sections
    if (lowerLine.includes('experience') || lowerLine.includes('work')) {
      currentSection = 'experience';
      continue;
    } else if (lowerLine.includes('education')) {
      currentSection = 'education';
      continue;
    } else if (lowerLine.includes('skills')) {
      currentSection = 'skills';
      continue;
    } else if (lowerLine.includes('summary') || lowerLine.includes('objective')) {
      currentSection = 'summary';
      continue;
    }

    // Parse contact info (usually at the top)
    if (!currentSection && !parsedData.contactInfo.name) {
      // First non-empty line is usually the name
      if (!parsedData.contactInfo.name && line.length > 0) {
        parsedData.contactInfo.name = line;
        continue;
      }
      
      // Look for email
      if (line.includes('@') && !parsedData.contactInfo.email) {
        parsedData.contactInfo.email = line;
        continue;
      }
      
      // Look for phone
      if (/\d{3}[-.]?\d{3}[-.]?\d{4}/.test(line) && !parsedData.contactInfo.phone) {
        parsedData.contactInfo.phone = line;
        continue;
      }
      
      // Look for LinkedIn
      if (line.includes('linkedin.com') && !parsedData.contactInfo.linkedin) {
        parsedData.contactInfo.linkedin = line;
        continue;
      }
      
      // Look for portfolio/website
      if ((line.includes('http') || line.includes('www')) && !parsedData.contactInfo.portfolio) {
        parsedData.contactInfo.portfolio = line;
        continue;
      }
    }

    // Parse summary
    if (currentSection === 'summary') {
      if (line.length > 0) {
        parsedData.summary += (parsedData.summary ? ' ' : '') + line;
      }
    }

    // Parse work experience
    if (currentSection === 'experience') {
      // Look for job titles (usually in caps or followed by company)
      if (line.includes(' at ') || line.includes(' - ') || line.includes(' | ')) {
        if (currentExperience) {
          parsedData.workExperience.push(currentExperience);
        }
        
        const parts = line.split(/ at | - | \| /);
        if (parts.length >= 2) {
          currentExperience = {
            title: parts[0].trim(),
            company: parts[1].trim(),
            location: '',
            startDate: '',
            endDate: '',
            description: []
          };
        }
      } else if (currentExperience && line.length > 0) {
        // Add as description bullet point
        if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
          currentExperience.description.push(line.substring(1).trim());
        } else {
          currentExperience.description.push(line);
        }
      }
    }

    // Parse education
    if (currentSection === 'education') {
      if (line.includes('University') || line.includes('College') || line.includes('School')) {
        if (currentEducation) {
          parsedData.education.push(currentEducation);
        }
        
        currentEducation = {
          degree: '',
          school: line,
          location: '',
          startDate: '',
          endDate: '',
          description: []
        };
      } else if (currentEducation && line.length > 0) {
        if (!currentEducation.degree && line.includes('Bachelor') || line.includes('Master') || line.includes('PhD')) {
          currentEducation.degree = line;
        } else {
          currentEducation.description.push(line);
        }
      }
    }

    // Parse skills
    if (currentSection === 'skills') {
      if (line.length > 0) {
        // Split by common separators
        const skills = line.split(/[,•\-|]/).map(skill => skill.trim()).filter(skill => skill);
        parsedData.skills.push(...skills);
      }
    }
  }

  // Add any remaining experience/education
  if (currentExperience) {
    parsedData.workExperience.push(currentExperience);
  }
  if (currentEducation) {
    parsedData.education.push(currentEducation);
  }

  return parsedData;
}

export const POST: APIRoute = async ({ request }) => {
    // Validate request content type
    if (request.headers.get('Content-Type') !== 'application/json') {
        return new Response(JSON.stringify({ 
            error: 'Invalid content type' 
        }), { 
            status: 400,
            headers: { 'Content-Type': 'application/json' } 
        });
    }

    try {
        const { fileBase64, fileName, fileType } = await request.json();
        
        // Validate input fields
        if (!fileBase64 || !fileName || !fileType) {
            return new Response(JSON.stringify({
                error: 'Missing required fields: fileBase64, fileName, or fileType'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Validate file type
        if (!SUPPORTED_MIME_TYPES.includes(fileType)) {
            return new Response(JSON.stringify({
                error: 'Unsupported file type',
                supportedTypes: SUPPORTED_MIME_TYPES
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Convert base64 to file buffer
        const fileBuffer = Buffer.from(fileBase64, 'base64');

        // Extract text directly from buffer
        const extractedText = await extractTextFromBuffer(fileBuffer, fileType);

        // Validate extracted text
        if (!extractedText || extractedText.trim().length === 0) {
            return new Response(JSON.stringify({ 
                success: false,
                error: 'No text could be extracted from the document',
                details: 'The document appears to be empty or unreadable'
            }), { 
                status: 400,
                headers: { 'Content-Type': 'application/json' } 
            });
        }

        // Clean resume text using Llama 3.1-8b-instant
        const cleanedText = await cleanResumeText(extractedText);

        // Parse the cleaned text into structured data
        const parsedResume = await parseResumeContent(cleanedText);

        return new Response(JSON.stringify({ 
            success: true, 
            data: {
                text: cleanedText,
                parsed: parsedResume
            }
        }), { 
            status: 200,
            headers: { 'Content-Type': 'application/json' } 
        });

    } catch (error) {
        console.error('Resume upload error:', error);
        
        const errorMessage = error instanceof Error 
            ? error.message 
            : 'An unexpected error occurred while processing the resume';

        return new Response(JSON.stringify({ 
            success: false,
            error: 'Failed to upload resume',
            details: errorMessage
        }), { 
            status: 500,
            headers: { 'Content-Type': 'application/json' } 
        });
    }
};